# Hijr Almanac - Prayer Times for Raycast

A Raycast extension that provides instant access to Muslim prayer times. Simply type "pray" in Raycast to see the next upcoming prayer time and all prayer times for today.

## Features

- **Instant Results**: Get prayer times immediately when typing "pray" in Raycast
- **Next Prayer Focus**: Shows the next upcoming prayer time prominently
- **Complete Schedule**: Displays all prayer times for the current day
- **Location Aware**: Automatically detects your location or uses manual coordinates
- **Multiple Calculation Methods**: Supports various Islamic calculation methods
- **Madhab Support**: Choose between Shafi/Maliki/Hanbali or Hanafi schools
- **Multi-Language Support**: Available in English and Bahasa Indonesia
- **Performance Optimized**: Uses intelligent caching for fast results

## Usage

### Quick Prayer Time (Instant Results)
1. Open Raycast (⌘ + Space)
2. Type "pray"
3. See your next prayer time instantly in a HUD notification!
4. The prayer time is automatically copied to your clipboard

### Detailed Prayer Times
1. Open Raycast (⌘ + Space)
2. Type "Prayer Times"
3. View all prayer times for today with detailed information

## Configuration

Access preferences through Raycast settings to customize:

- **Location**: Set manual coordinates or let the extension auto-detect
- **Calculation Method**: Choose from Muslim World League, Egyptian, Karachi, etc.
- **Madhab**: Select Shafi/Maliki/Hanbali or Hanafi for Asr calculation
- **Language**: Choose between English and Bahasa Indonesia
- **City Name**: Optional display name for your location

## Supported Calculation Methods

- Muslim World League (default)
- Egyptian General Authority of Survey
- University of Islamic Sciences, Karachi
- Umm Al-Qura University, Makkah
- Islamic Society of North America
- Moonsighting Committee Worldwide
- Dubai, Kuwait, Qatar regional methods

## Technical Details

- Built with TypeScript and React
- Uses the `adhan` library for accurate prayer time calculations
- Implements intelligent caching for performance
- Supports high latitude locations with special calculation rules
- Handles timezone detection automatically

## Development

To test the prayer time calculations:

```typescript
import { PrayerTimeTest } from './src/test/PrayerTimeTest';
PrayerTimeTest.runTests();
```

To test the internationalization:

```typescript
import { I18nTest } from './src/test/I18nTest';
I18nTest.runTests();
```

## Privacy

This extension:
- Only accesses location data when needed for prayer time calculations
- Caches location data locally for performance
- Does not send any data to external servers (except for optional reverse geocoding)
- All prayer time calculations are performed locally