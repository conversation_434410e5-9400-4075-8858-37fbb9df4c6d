import { updateCommandMetadata, Clipboard, showToast, Toast } from "@raycast/api";
import { HijrAlmanacService } from "./services/HijrAlmanacService";
import { PrayerTimeService } from "./services/PrayerTimeService";

export default async function Command() {
  try {
    // Get the next prayer time
    const nextPrayerData = await HijrAlmanacService.getNextPrayerTime();
    const { result: nextPrayer, location } = nextPrayerData;

    // Format the prayer time for display
    const timeStr = PrayerTimeService.formatPrayerTime(nextPrayer.prayer.time);
    const locationStr = location.city ? `${location.city}${location.country ? `, ${location.country}` : ''}` : 'Current Location';

    // Create the subtitle for Raycast search interface
    let subtitle = `${nextPrayer.prayer.displayName} - ${timeStr}`;

    if (nextPrayer.timeUntil) {
      subtitle += ` (in ${nextPrayer.timeUntil})`;
    }

    if (!nextPrayer.isToday) {
      subtitle += ' (Tomorrow)';
    }

    subtitle += ` • ${locationStr}`;

    // Copy to clipboard for easy access
    const clipboardContent = `${nextPrayer.prayer.displayName} - ${timeStr}`;
    await Clipboard.copy(clipboardContent);

    // Update the command metadata to show result in Raycast search interface
    await updateCommandMetadata({ subtitle });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to load prayer times';

    // Update subtitle to show error
    await updateCommandMetadata({ subtitle: `Error: ${errorMessage}` });

    await showToast({
      style: Toast.Style.Failure,
      title: "Prayer Time Error",
      message: errorMessage
    });
  }
}
