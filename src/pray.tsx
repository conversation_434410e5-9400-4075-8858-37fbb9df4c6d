import { updateCommandMetadata, Clipboard, showToast, Toast } from "@raycast/api";
import { HijrAlmanacService } from "./services/HijrAlmanacService";
import { PrayerTimeService } from "./services/PrayerTimeService";
import { I18nService } from "./services/I18nService";

export default async function Command() {
  try {
    // Get the next prayer time
    const nextPrayerData = await HijrAlmanacService.getNextPrayerTime();
    const { result: nextPrayer, location } = nextPrayerData;

    // Format the prayer time for display
    const timeStr = PrayerTimeService.formatPrayerTime(nextPrayer.prayer.time);
    const locationStr = location.city ? `${location.city}${location.country ? `, ${location.country}` : ''}` : I18nService.t('ui.currentLocation');

    // Create the subtitle for Raycast search interface
    let subtitle = `${nextPrayer.prayer.displayName} - ${timeStr}`;

    if (nextPrayer.timeUntil) {
      subtitle += ` (${I18nService.t('ui.in')} ${nextPrayer.timeUntil})`;
    }

    if (!nextPrayer.isToday) {
      subtitle += ` (${I18nService.t('ui.tomorrow')})`;
    }

    subtitle += ` • ${locationStr}`;

    // Copy to clipboard for easy access
    const clipboardContent = `${nextPrayer.prayer.displayName} - ${timeStr}`;
    await Clipboard.copy(clipboardContent);

    // Update the command metadata to show result in Raycast search interface
    await updateCommandMetadata({ subtitle });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to load prayer times';

    // Update subtitle to show error
    await updateCommandMetadata({ subtitle: `${I18nService.t('ui.error')}: ${errorMessage}` });

    await showToast({
      style: Toast.Style.Failure,
      title: I18nService.t('ui.prayerTimeError'),
      message: errorMessage
    });
  }
}
