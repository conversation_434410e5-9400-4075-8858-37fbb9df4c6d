import {
  PrayerTimes as AdhanPrayerT<PERSON>,
  Coordinates,
  CalculationMethod,
  Madhab,
  HighLatitudeRule
} from 'adhan';
import { PrayerTime, PrayerTimes, Location, NextPrayerResult, PrayerServiceConfig } from '../types';
import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/ErrorHandler';
import { Cache } from '../utils/Cache';
import { Performance } from '../utils/Performance';
import { I18nService } from './I18nService';

export class PrayerTimeService {
  private static readonly CACHE_TTL = 60 * 60 * 1000; // 1 hour

  /**
   * Calculate prayer times for a given location and date with caching
   */
  static calculatePrayerTimes(
    location: Location,
    date: Date = new Date(),
    config: PrayerServiceConfig = {}
  ): PrayerTimes {
    try {
      // Validate inputs
      ErrorHandler.validatePrayerInputs(location.latitude, location.longitude, date);

      // Create cache key
      const dateStr = date.toDateString();
      const cacheKey = `prayer-times-${location.latitude}-${location.longitude}-${dateStr}-${JSON.stringify(config)}`;

      // Try to get from cache first
      const cached = Cache.get<PrayerTimes>(cacheKey);
      if (cached) {
        return cached;
      }

      // Calculate fresh prayer times
      const coordinates = new Coordinates(location.latitude, location.longitude);
      const params = this.getCalculationParams(config);
      const prayerTimes = new AdhanPrayerTimes(coordinates, date, params);

      const result: PrayerTimes = {
        fajr: prayerTimes.fajr,
        sunrise: prayerTimes.sunrise,
        dhuhr: prayerTimes.dhuhr,
        asr: prayerTimes.asr,
        maghrib: prayerTimes.maghrib,
        isha: prayerTimes.isha
      };

      // Cache the result
      Cache.set(cacheKey, result, this.CACHE_TTL);

      return result;
    } catch (error) {
      throw new Error(ErrorHandler.logAndFormat(error, 'PrayerTimeService.calculatePrayerTimes'));
    }
  }

  /**
   * Get the next upcoming prayer time
   */
  static getNextPrayer(
    location: Location,
    config: PrayerServiceConfig = {}
  ): NextPrayerResult {
    Performance.startTimer('getNextPrayer');
    try {
      const now = new Date();
      const todayPrayers = this.calculatePrayerTimes(location, now, config);

      // Convert to array of prayer times for easier processing
      const prayerArray: PrayerTime[] = [
        { name: 'fajr', time: todayPrayers.fajr, displayName: I18nService.getPrayerDisplayName('fajr') },
        { name: 'dhuhr', time: todayPrayers.dhuhr, displayName: I18nService.getPrayerDisplayName('dhuhr') },
        { name: 'asr', time: todayPrayers.asr, displayName: I18nService.getPrayerDisplayName('asr') },
        { name: 'maghrib', time: todayPrayers.maghrib, displayName: I18nService.getPrayerDisplayName('maghrib') },
        { name: 'isha', time: todayPrayers.isha, displayName: I18nService.getPrayerDisplayName('isha') }
      ];

      // Find next prayer today
      const nextTodayPrayer = prayerArray.find(prayer => prayer.time > now);

      if (nextTodayPrayer) {
        const result = {
          prayer: nextTodayPrayer,
          timeUntil: this.formatTimeUntil(nextTodayPrayer.time, now),
          isToday: true
        };
        Performance.endTimer('getNextPrayer');
        return result;
      }

      // If no prayer left today, get tomorrow's Fajr
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowPrayers = this.calculatePrayerTimes(location, tomorrow, config);

      const tomorrowFajr: PrayerTime = {
        name: 'fajr',
        time: tomorrowPrayers.fajr,
        displayName: I18nService.getPrayerDisplayName('fajr')
      };

      const result = {
        prayer: tomorrowFajr,
        timeUntil: this.formatTimeUntil(tomorrowFajr.time, now),
        isToday: false
      };

      Performance.endTimer('getNextPrayer');
      return result;
    } catch (error) {
      Performance.endTimer('getNextPrayer');
      throw error;
    }
  }

  /**
   * Get all prayer times for today
   */
  static getTodayPrayers(
    location: Location,
    config: PrayerServiceConfig = {}
  ): PrayerTime[] {
    const today = new Date();
    const prayerTimes = this.calculatePrayerTimes(location, today, config);

    return [
      { name: 'fajr', time: prayerTimes.fajr, displayName: I18nService.getPrayerDisplayName('fajr') },
      { name: 'sunrise', time: prayerTimes.sunrise, displayName: I18nService.getPrayerDisplayName('sunrise') },
      { name: 'dhuhr', time: prayerTimes.dhuhr, displayName: I18nService.getPrayerDisplayName('dhuhr') },
      { name: 'asr', time: prayerTimes.asr, displayName: I18nService.getPrayerDisplayName('asr') },
      { name: 'maghrib', time: prayerTimes.maghrib, displayName: I18nService.getPrayerDisplayName('maghrib') },
      { name: 'isha', time: prayerTimes.isha, displayName: I18nService.getPrayerDisplayName('isha') }
    ];
  }

  /**
   * Format time until next prayer
   */
  private static formatTimeUntil(prayerTime: Date, currentTime: Date): string {
    return I18nService.formatTimeUntil(prayerTime, currentTime);
  }

  /**
   * Format prayer time for display
   */
  static formatPrayerTime(time: Date): string {
    return time.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }

  /**
   * Get calculation parameters based on config
   */
  private static getCalculationParams(config: PrayerServiceConfig) {
    const params = CalculationMethod.MuslimWorldLeague();

    // Set calculation method
    if (config.calculationMethod) {
      switch (config.calculationMethod) {
        case 'Egyptian':
          return CalculationMethod.Egyptian();
        case 'Karachi':
          return CalculationMethod.Karachi();
        case 'UmmAlQura':
          return CalculationMethod.UmmAlQura();
        case 'Dubai':
          return CalculationMethod.Dubai();
        case 'MoonsightingCommittee':
          return CalculationMethod.MoonsightingCommittee();
        case 'NorthAmerica':
          return CalculationMethod.NorthAmerica();
        case 'Kuwait':
          return CalculationMethod.Kuwait();
        case 'Qatar':
          return CalculationMethod.Qatar();
        default:
          break;
      }
    }

    // Set madhab
    if (config.madhab) {
      params.madhab = config.madhab === 'Hanafi' ? Madhab.Hanafi : Madhab.Shafi;
    }

    // Set high latitude rule
    if (config.highLatitudeRule) {
      switch (config.highLatitudeRule) {
        case 'SeventhOfTheNight':
          params.highLatitudeRule = HighLatitudeRule.SeventhOfTheNight;
          break;
        case 'TwilightAngle':
          params.highLatitudeRule = HighLatitudeRule.TwilightAngle;
          break;
        default:
          params.highLatitudeRule = HighLatitudeRule.MiddleOfTheNight;
      }
    }

    return params;
  }
}
