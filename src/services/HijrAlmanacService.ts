import { getPreferenceValues } from '@raycast/api';
import { LocationService } from './LocationService';
import { PrayerTimeService } from './PrayerTimeService';
import { I18nService } from './I18nService';
import { Location, NextPrayerResult, PrayerTime, PrayerServiceConfig, ExtensionPreferences } from '../types';
import { ErrorHandler } from '../utils/ErrorHandler';



export class HijrAlmanacService {
  private static config: PrayerServiceConfig = {
    calculationMethod: 'MuslimWorldLeague',
    madhab: 'Shafi',
    highLatitudeRule: 'MiddleOfTheNight'
  };

  private static cachedLocation: Location | null = null;
  private static lastLocationUpdate = 0;
  private static readonly LOCATION_CACHE_TTL = 30 * 60 * 1000; // 30 minutes

  /**
   * Get the next prayer time with location detection
   */
  static async getNextPrayerTime(): Promise<{
    result: NextPrayerResult;
    location: Location;
  }> {
    try {
      const location = await this.getLocationFromPreferences();
      const config = this.getConfigFromPreferences();
      const result = PrayerTimeService.getNextPrayer(location, config);

      return { result, location };
    } catch (error) {
      const message = ErrorHandler.logAndFormat(error, 'HijrAlmanacService.getNextPrayerTime');
      throw new Error(message);
    }
  }

  /**
   * Get all prayer times for today
   */
  static async getTodayPrayerTimes(): Promise<{
    prayers: PrayerTime[];
    location: Location;
  }> {
    try {
      const location = await this.getLocationFromPreferences();
      const config = this.getConfigFromPreferences();
      const prayers = PrayerTimeService.getTodayPrayers(location, config);

      return { prayers, location };
    } catch (error) {
      const message = ErrorHandler.logAndFormat(error, 'HijrAlmanacService.getTodayPrayerTimes');
      throw new Error(message);
    }
  }

  /**
   * Format next prayer for display
   */
  static formatNextPrayerDisplay(result: NextPrayerResult, location: Location): string {
    const timeStr = PrayerTimeService.formatPrayerTime(result.prayer.time);
    const locationStr = location.city ? `${location.city}${location.country ? `, ${location.country}` : ''}` : I18nService.t('ui.currentLocation');

    let displayText = `**${result.prayer.displayName}** - ${timeStr}`;

    if (result.timeUntil) {
      displayText += ` (${I18nService.t('ui.in')} ${result.timeUntil})`;
    }

    if (!result.isToday) {
      displayText += ` (${I18nService.t('ui.tomorrow')})`;
    }

    displayText += `\n\n📍 ${locationStr}`;

    return displayText;
  }

  /**
   * Format all prayer times for display
   */
  static formatAllPrayersDisplay(prayers: PrayerTime[], location: Location): string {
    const locationStr = location.city ? `${location.city}${location.country ? `, ${location.country}` : ''}` : I18nService.t('ui.currentLocation');
    const now = new Date();

    let displayText = `# ${I18nService.t('ui.prayerTimes')}\n\n📍 **${locationStr}**\n\n`;

    prayers.forEach(prayer => {
      const timeStr = PrayerTimeService.formatPrayerTime(prayer.time);
      const isPast = prayer.time < now;
      const isCurrent = !isPast && prayers.find(p => p.time > now)?.name === prayer.name;

      let line = `**${prayer.displayName}**: ${timeStr}`;

      if (isCurrent) {
        line += ` ← **${I18nService.t('ui.next')}**`;
      } else if (isPast) {
        line += ' ✓';
      }

      displayText += `${line}\n\n`;
    });

    return displayText;
  }

  /**
   * Update configuration
   */
  static updateConfig(newConfig: Partial<PrayerServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  static getConfig(): PrayerServiceConfig {
    return { ...this.config };
  }

  /**
   * Clear location cache (useful for testing or when user moves)
   */
  static clearLocationCache(): void {
    LocationService.clearCache();
    this.cachedLocation = null;
    this.lastLocationUpdate = 0;
  }

  /**
   * Clear all caches (location and language)
   */
  static clearAllCaches(): void {
    this.clearLocationCache();
    I18nService.clearCache();
  }

  /**
   * Get location from preferences or fallback to auto-detection with caching
   */
  private static async getLocationFromPreferences(): Promise<Location> {
    try {
      // Check if we have a valid cached location
      const now = Date.now();
      if (this.cachedLocation && (now - this.lastLocationUpdate) < this.LOCATION_CACHE_TTL) {
        return this.cachedLocation;
      }

      const preferences = getPreferenceValues<ExtensionPreferences>();

      // If user has set custom coordinates, use them
      if (preferences.latitude && preferences.longitude) {
        const lat = parseFloat(preferences.latitude);
        const lon = parseFloat(preferences.longitude);

        if (!isNaN(lat) && !isNaN(lon)) {
          const location = {
            latitude: lat,
            longitude: lon,
            city: preferences.city || undefined,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
          };

          // Cache the location
          this.cachedLocation = location;
          this.lastLocationUpdate = now;
          return location;
        }
      }

      // Otherwise, try auto-detection
      const location = await LocationService.getCurrentLocation();
      this.cachedLocation = location;
      this.lastLocationUpdate = now;
      return location;
    } catch (error) {
      console.warn('Failed to get location from preferences, using default:', error);
      const defaultLocation = LocationService.getDefaultLocation();
      this.cachedLocation = defaultLocation;
      this.lastLocationUpdate = Date.now();
      return defaultLocation;
    }
  }

  /**
   * Get prayer calculation config from preferences
   */
  private static getConfigFromPreferences(): PrayerServiceConfig {
    try {
      const preferences = getPreferenceValues<ExtensionPreferences>();

      return {
        calculationMethod: (preferences.calculationMethod as any) || 'MuslimWorldLeague',
        madhab: (preferences.madhab as any) || 'Shafi',
        highLatitudeRule: 'MiddleOfTheNight'
      };
    } catch (error) {
      console.warn('Failed to get config from preferences, using defaults:', error);
      return this.config;
    }
  }
}
