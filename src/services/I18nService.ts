import { getPreferenceValues } from '@raycast/api';
import { Language, Translations, translations } from '../i18n/translations';

interface Preferences {
  language?: string;
}

export class I18nService {
  private static currentLanguage: Language | null = null;
  private static currentTranslations: Translations | null = null;

  /**
   * Get the current language from preferences
   */
  static getCurrentLanguage(): Language {
    if (this.currentLanguage) {
      return this.currentLanguage;
    }

    try {
      const preferences = getPreferenceValues<Preferences>();
      const language = (preferences.language as Language) || 'english';
      
      // Validate language
      if (language === 'english' || language === 'indonesia') {
        this.currentLanguage = language;
        return language;
      }
    } catch (error) {
      console.warn('Failed to get language preference, using default:', error);
    }

    // Default to English
    this.currentLanguage = 'english';
    return this.currentLanguage;
  }

  /**
   * Get translations for the current language
   */
  static getTranslations(): Translations {
    if (this.currentTranslations) {
      return this.currentTranslations;
    }

    const language = this.getCurrentLanguage();
    this.currentTranslations = translations[language];
    return this.currentTranslations;
  }

  /**
   * Get a specific translation by key path
   */
  static t(keyPath: string): string {
    const trans = this.getTranslations();
    const keys = keyPath.split('.');
    
    let value: any = trans;
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        console.warn(`Translation key not found: ${keyPath}`);
        return keyPath; // Return the key path as fallback
      }
    }

    return typeof value === 'string' ? value : keyPath;
  }

  /**
   * Clear cached language and translations (useful for testing or preference changes)
   */
  static clearCache(): void {
    this.currentLanguage = null;
    this.currentTranslations = null;
  }

  /**
   * Format time until next prayer with localized units
   */
  static formatTimeUntil(prayerTime: Date, currentTime: Date): string {
    const diff = prayerTime.getTime() - currentTime.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    const trans = this.getTranslations();

    if (hours > 0) {
      return `${hours}${trans.time.hoursShort} ${minutes}${trans.time.minutesShort}`;
    } else {
      return `${minutes}${trans.time.minutesShort}`;
    }
  }

  /**
   * Get prayer display name by prayer name
   */
  static getPrayerDisplayName(prayerName: string): string {
    const trans = this.getTranslations();
    
    switch (prayerName) {
      case 'fajr':
        return trans.prayers.fajr;
      case 'sunrise':
        return trans.prayers.sunrise;
      case 'dhuhr':
        return trans.prayers.dhuhr;
      case 'asr':
        return trans.prayers.asr;
      case 'maghrib':
        return trans.prayers.maghrib;
      case 'isha':
        return trans.prayers.isha;
      default:
        return prayerName;
    }
  }
}
