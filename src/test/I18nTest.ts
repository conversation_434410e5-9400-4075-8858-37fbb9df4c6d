import { I18nService } from '../services/I18nService';
import { translations } from '../i18n/translations';

/**
 * Simple test to verify i18n functionality
 */
export class I18nTest {
  static runTests(): void {
    console.log('Running I18n tests...');

    // Test 1: Check if all translation keys exist for both languages
    this.testTranslationCompleteness();

    // Test 2: Test prayer name translations
    this.testPrayerNames();

    // Test 3: Test time formatting
    this.testTimeFormatting();

    console.log('I18n tests completed successfully!');
  }

  private static testTranslationCompleteness(): void {
    const englishKeys = this.getTranslationKeys(translations.english);
    const indonesiaKeys = this.getTranslationKeys(translations.indonesia);

    if (englishKeys.length !== indonesiaKeys.length) {
      throw new Error('Translation key count mismatch between languages');
    }

    for (const key of englishKeys) {
      if (!indonesiaKeys.includes(key)) {
        throw new Error(`Missing Indonesian translation for key: ${key}`);
      }
    }

    console.log('✓ Translation completeness test passed');
  }

  private static testPrayerNames(): void {
    // Clear cache to ensure fresh test
    I18nService.clearCache();

    const prayerNames = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];

    for (const prayerName of prayerNames) {
      const englishName = I18nService.getPrayerDisplayName(prayerName);
      const indonesiaName = I18nService.getPrayerDisplayName(prayerName);

      if (!englishName || !indonesiaName) {
        throw new Error(`Missing prayer name translation for: ${prayerName}`);
      }
    }

    // Test calculation method names
    const methodName = I18nService.getCalculationMethodName('MuslimWorldLeague');
    if (!methodName) {
      throw new Error('Missing calculation method name');
    }

    // Test madhab names
    const madhabName = I18nService.getMadhabName('Shafi');
    if (!madhabName) {
      throw new Error('Missing madhab name');
    }

    // Test prayer status
    const statusText = I18nService.getPrayerStatusText(false, true);
    if (!statusText) {
      throw new Error('Missing prayer status text');
    }

    console.log('✓ Prayer names and UI elements test passed');
  }

  private static testTimeFormatting(): void {
    const now = new Date();
    const futureTime = new Date(now.getTime() + 2 * 60 * 60 * 1000 + 30 * 60 * 1000); // 2h 30m later

    const timeUntil = I18nService.formatTimeUntil(futureTime, now);
    
    if (!timeUntil.includes('2') || !timeUntil.includes('30')) {
      throw new Error('Time formatting test failed');
    }

    console.log('✓ Time formatting test passed');
  }

  private static getTranslationKeys(obj: any, prefix = ''): string[] {
    let keys: string[] = [];
    
    for (const key in obj) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        keys = keys.concat(this.getTranslationKeys(obj[key], fullKey));
      } else {
        keys.push(fullKey);
      }
    }
    
    return keys;
  }
}
