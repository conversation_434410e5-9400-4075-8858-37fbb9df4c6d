export type Language = 'english' | 'indonesia';

export interface Translations {
  prayers: {
    fajr: string;
    sunrise: string;
    dhuhr: string;
    asr: string;
    maghrib: string;
    isha: string;
  };
  ui: {
    loading: string;
    error: string;
    retry: string;
    refresh: string;
    refreshing: string;
    updated: string;
    copyNextPrayer: string;
    nextPrayer: string;
    prayerTimes: string;
    currentLocation: string;
    tomorrow: string;
    next: string;
    in: string;
    prayerTimeError: string;
    checkConnection: string;
    // Enhanced UI translations
    upcomingPrayer: string;
    todaySchedule: string;
    locationInfo: string;
    calculationSettings: string;
    method: string;
    school: string;
    completed: string;
    current: string;
    upcoming: string;
    timeRemaining: string;
    copyAllTimes: string;
    openSettings: string;
    lastUpdated: string;
    status: string;
    details: string;
  };
  time: {
    hours: string;
    minutes: string;
    hoursShort: string;
    minutesShort: string;
  };
}

export const translations: Record<Language, Translations> = {
  english: {
    prayers: {
      fajr: 'Fajr (Dawn)',
      sunrise: 'Sunrise',
      dhuhr: 'Dhuhr (Noon)',
      asr: 'Asr (Afternoon)',
      maghrib: 'Maghrib (Sunset)',
      isha: 'Isha (Night)'
    },
    ui: {
      loading: 'Loading prayer times...',
      error: 'Error',
      retry: 'Retry',
      refresh: 'Refresh',
      refreshing: 'Refreshing prayer times...',
      updated: 'Prayer times updated',
      copyNextPrayer: 'Copy Next Prayer Time',
      nextPrayer: 'Next Prayer',
      prayerTimes: 'Prayer Times',
      currentLocation: 'Current Location',
      tomorrow: 'Tomorrow',
      next: 'Next',
      in: 'in',
      prayerTimeError: 'Prayer Time Error',
      checkConnection: 'Please check your internet connection and location permissions.',
      // Enhanced UI translations
      upcomingPrayer: 'Upcoming Prayer',
      todaySchedule: "Today's Schedule",
      locationInfo: 'Location Information',
      calculationSettings: 'Calculation Settings',
      method: 'Method',
      school: 'School',
      completed: 'Completed',
      current: 'Current',
      upcoming: 'Upcoming',
      timeRemaining: 'Time Remaining',
      copyAllTimes: 'Copy All Prayer Times',
      openSettings: 'Open Settings',
      lastUpdated: 'Last Updated',
      status: 'Status',
      details: 'Details'
    },
    time: {
      hours: 'hours',
      minutes: 'minutes',
      hoursShort: 'h',
      minutesShort: 'm'
    }
  },
  indonesia: {
    prayers: {
      fajr: 'Subuh (Fajar)',
      sunrise: 'Terbit',
      dhuhr: 'Dzuhur (Siang)',
      asr: 'Ashar (Sore)',
      maghrib: 'Maghrib (Senja)',
      isha: 'Isya (Malam)'
    },
    ui: {
      loading: 'Memuat waktu sholat...',
      error: 'Kesalahan',
      retry: 'Coba Lagi',
      refresh: 'Perbarui',
      refreshing: 'Memperbarui waktu sholat...',
      updated: 'Waktu sholat diperbarui',
      copyNextPrayer: 'Salin Waktu Sholat Berikutnya',
      nextPrayer: 'Sholat Berikutnya',
      prayerTimes: 'Waktu Sholat',
      currentLocation: 'Lokasi Saat Ini',
      tomorrow: 'Besok',
      next: 'Berikutnya',
      in: 'dalam',
      prayerTimeError: 'Kesalahan Waktu Sholat',
      checkConnection: 'Silakan periksa koneksi internet dan izin lokasi Anda.',
      // Enhanced UI translations
      upcomingPrayer: 'Sholat Berikutnya',
      todaySchedule: 'Jadwal Hari Ini',
      locationInfo: 'Informasi Lokasi',
      calculationSettings: 'Pengaturan Perhitungan',
      method: 'Metode',
      school: 'Mazhab',
      completed: 'Selesai',
      current: 'Saat Ini',
      upcoming: 'Akan Datang',
      timeRemaining: 'Waktu Tersisa',
      copyAllTimes: 'Salin Semua Waktu Sholat',
      openSettings: 'Buka Pengaturan',
      lastUpdated: 'Terakhir Diperbarui',
      status: 'Status',
      details: 'Detail'
    },
    time: {
      hours: 'jam',
      minutes: 'menit',
      hoursShort: 'j',
      minutesShort: 'm'
    }
  }
};
