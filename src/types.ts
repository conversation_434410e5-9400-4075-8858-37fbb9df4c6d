export interface PrayerTime {
  name: string;
  time: Date;
  displayName: string;
}

export interface PrayerTimes {
  fajr: Date;
  sunrise: Date;
  dhuhr: Date;
  asr: Date;
  maghrib: Date;
  isha: Date;
}

export interface Location {
  latitude: number;
  longitude: number;
  city?: string;
  country?: string;
  timezone?: string;
}

export interface NextPrayerResult {
  prayer: PrayerTime;
  timeUntil: string;
  isToday: boolean;
}

export interface PrayerServiceConfig {
  calculationMethod?: 'MuslimWorldLeague' | 'Egyptian' | 'Karachi' | 'UmmAlQura' | 'Dubai' | 'MoonsightingCommittee' | 'NorthAmerica' | 'Kuwait' | 'Qatar';
  madhab?: 'Shafi' | 'Hanafi';
  highLatitudeRule?: 'MiddleOfTheNight' | 'SeventhOfTheNight' | 'TwilightAngle';
}

export interface ExtensionPreferences {
  latitude?: string;
  longitude?: string;
  city?: string;
  calculationMethod?: string;
  madhab?: string;
  language?: string;
}
