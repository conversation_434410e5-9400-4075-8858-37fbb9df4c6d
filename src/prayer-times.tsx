import { Detail, ActionPanel, Action, showToast, Toast } from "@raycast/api";
import { useEffect, useState } from "react";
import { HijrAlmanacService } from "./services/HijrAlmanacService";
import { PrayerTimeService } from "./services/PrayerTimeService";
import { I18nService } from "./services/I18nService";
import { NextPrayerResult, Location, PrayerTime } from "./types";

interface PrayerData {
  nextPrayer: NextPrayerResult;
  allPrayers: PrayerTime[];
  location: Location;
}

export default function Command() {
  const [prayerData, setPrayerData] = useState<PrayerData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPrayerTimes();
  }, []);

  const loadPrayerTimes = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load both next prayer and all prayers concurrently
      const [nextPrayerData, todayPrayersData] = await Promise.all([
        HijrAlmanacService.getNextPrayerTime(),
        HijrAlmanacService.getTodayPrayerTimes()
      ]);

      setPrayerData({
        nextPrayer: nextPrayerData.result,
        allPrayers: todayPrayersData.prayers,
        location: nextPrayerData.location
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load prayer times';
      setError(errorMessage);
      showToast({
        style: Toast.Style.Failure,
        title: "Error",
        message: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPrayerTimes = async () => {
    showToast({
      style: Toast.Style.Animated,
      title: I18nService.t('ui.refreshing')
    });

    HijrAlmanacService.clearLocationCache();
    await loadPrayerTimes();

    showToast({
      style: Toast.Style.Success,
      title: I18nService.t('ui.updated')
    });
  };

  if (isLoading) {
    return <Detail isLoading={true} markdown={I18nService.t('ui.loading')} />;
  }

  if (error || !prayerData) {
    return (
      <Detail
        markdown={`# ${I18nService.t('ui.error')}\n\n${error || 'Failed to load prayer times'}\n\n${I18nService.t('ui.checkConnection')}`}
        actions={
          <ActionPanel>
            <Action title={I18nService.t('ui.retry')} onAction={loadPrayerTimes} />
          </ActionPanel>
        }
      />
    );
  }

  const nextPrayerDisplay = HijrAlmanacService.formatNextPrayerDisplay(
    prayerData.nextPrayer,
    prayerData.location
  );

  const allPrayersDisplay = HijrAlmanacService.formatAllPrayersDisplay(
    prayerData.allPrayers,
    prayerData.location
  );

  return (
    <Detail
      markdown={`# ${I18nService.t('ui.nextPrayer')}\n\n${nextPrayerDisplay}\n\n---\n\n${allPrayersDisplay}`}
      actions={
        <ActionPanel>
          <Action title={I18nService.t('ui.refresh')} onAction={refreshPrayerTimes} />
          <Action.CopyToClipboard
            title={I18nService.t('ui.copyNextPrayer')}
            content={`${prayerData.nextPrayer.prayer.displayName} - ${PrayerTimeService.formatPrayerTime(prayerData.nextPrayer.prayer.time)}`}
          />
        </ActionPanel>
      }
    />
  );
}
