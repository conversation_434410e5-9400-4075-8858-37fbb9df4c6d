import { Detail, ActionPanel, Action, showToast, Toast, Icon, Color, openExtensionPreferences } from "@raycast/api";
import { useEffect, useState } from "react";
import { HijrAlmanacService } from "./services/HijrAlmanacService";
import { PrayerTimeService } from "./services/PrayerTimeService";
import { I18nService } from "./services/I18nService";
import { NextPrayerResult, Location, PrayerTime, ExtensionPreferences } from "./types";
import { getPreferenceValues } from "@raycast/api";

interface PrayerData {
  nextPrayer: NextPrayerResult;
  allPrayers: PrayerTime[];
  location: Location;
  preferences: ExtensionPreferences;
  lastUpdated: Date;
}

interface PrayerStatus {
  isPast: boolean;
  isCurrent: boolean;
  isNext: boolean;
}

// Helper function to get prayer status
function getPrayerStatus(prayer: PrayerTime, allPrayers: PrayerTime[]): PrayerStatus {
  const now = new Date();
  const isPast = prayer.time < now;
  const nextPrayer = allPrayers.find(p => p.time > now);
  const isCurrent = !isPast && nextPrayer?.name === prayer.name;
  const isNext = isCurrent;

  return { isPast, isCurrent, isNext };
}

// Helper function to get prayer icon based on status
function getPrayerIcon(status: PrayerStatus): string {
  if (status.isCurrent) {
    return "⏰";
  } else if (status.isPast) {
    return "✅";
  } else {
    return "⏳";
  }
}

// Helper function to get status color
function getStatusColor(status: PrayerStatus): Color {
  if (status.isCurrent) {
    return Color.Orange;
  } else if (status.isPast) {
    return Color.Green;
  } else {
    return Color.Blue;
  }
}

export default function Command() {
  const [prayerData, setPrayerData] = useState<PrayerData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPrayerTimes();
  }, []);

  const loadPrayerTimes = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load both next prayer and all prayers concurrently
      const [nextPrayerData, todayPrayersData] = await Promise.all([
        HijrAlmanacService.getNextPrayerTime(),
        HijrAlmanacService.getTodayPrayerTimes()
      ]);

      const preferences = getPreferenceValues<ExtensionPreferences>();

      setPrayerData({
        nextPrayer: nextPrayerData.result,
        allPrayers: todayPrayersData.prayers,
        location: nextPrayerData.location,
        preferences,
        lastUpdated: new Date()
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load prayer times';
      setError(errorMessage);
      showToast({
        style: Toast.Style.Failure,
        title: "Error",
        message: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPrayerTimes = async () => {
    showToast({
      style: Toast.Style.Animated,
      title: I18nService.t('ui.refreshing')
    });

    HijrAlmanacService.clearLocationCache();
    await loadPrayerTimes();

    showToast({
      style: Toast.Style.Success,
      title: I18nService.t('ui.updated')
    });
  };

  if (isLoading) {
    return (
      <Detail
        isLoading={true}
        markdown={`# 🕌 ${I18nService.t('ui.prayerTimes')}\n\n${I18nService.t('ui.loading')}`}
        metadata={
          <Detail.Metadata>
            <Detail.Metadata.Label
              title={I18nService.t('ui.status')}
              text={{
                value: I18nService.t('ui.loading'),
                color: Color.Blue
              }}
              icon="⏳"
            />
          </Detail.Metadata>
        }
      />
    );
  }

  if (error || !prayerData) {
    return (
      <Detail
        markdown={`# ⚠️ ${I18nService.t('ui.error')}\n\n${error || 'Failed to load prayer times'}\n\n${I18nService.t('ui.checkConnection')}`}
        metadata={
          <Detail.Metadata>
            <Detail.Metadata.Label
              title={I18nService.t('ui.status')}
              text={{
                value: I18nService.t('ui.error'),
                color: Color.Red
              }}
              icon="❌"
            />
            <Detail.Metadata.Separator />
            <Detail.Metadata.Label
              title={I18nService.t('ui.details')}
              text={error || 'Failed to load prayer times'}
            />
          </Detail.Metadata>
        }
        actions={
          <ActionPanel>
            <Action
              title={I18nService.t('ui.retry')}
              icon={Icon.ArrowClockwise}
              onAction={loadPrayerTimes}
            />
            <Action
              title={I18nService.t('ui.openSettings')}
              icon={Icon.Gear}
              onAction={openExtensionPreferences}
            />
          </ActionPanel>
        }
      />
    );
  }

  // Create enhanced markdown content
  const nextPrayer = prayerData.nextPrayer;
  const timeStr = PrayerTimeService.formatPrayerTime(nextPrayer.prayer.time);
  const locationStr = prayerData.location.city
    ? `${prayerData.location.city}${prayerData.location.country ? `, ${prayerData.location.country}` : ''}`
    : I18nService.t('ui.currentLocation');

  let markdown = `# 🕌 ${I18nService.t('ui.upcomingPrayer')}\n\n`;
  markdown += `## ${nextPrayer.prayer.displayName}\n\n`;
  markdown += `**${timeStr}**`;

  if (nextPrayer.timeUntil) {
    markdown += ` • ${I18nService.t('ui.in')} ${nextPrayer.timeUntil}`;
  }

  if (!nextPrayer.isToday) {
    markdown += ` • ${I18nService.t('ui.tomorrow')}`;
  }

  markdown += `\n\n📍 ${locationStr}`;

  // Create all prayer times content for clipboard
  const allTimesContent = prayerData.allPrayers
    .map(prayer => `${prayer.displayName}: ${PrayerTimeService.formatPrayerTime(prayer.time)}`)
    .join('\n');

  return (
    <Detail
      markdown={markdown}
      metadata={
        <Detail.Metadata>
          <Detail.Metadata.Label
            title={I18nService.t('ui.todaySchedule')}
            icon="📅"
          />
          <Detail.Metadata.Separator />

          {prayerData.allPrayers.map((prayer) => {
            const status = getPrayerStatus(prayer, prayerData.allPrayers);
            const statusText = I18nService.getPrayerStatusText(status.isPast, status.isCurrent);
            const icon = getPrayerIcon(status);
            const color = getStatusColor(status);

            return (
              <Detail.Metadata.Label
                key={prayer.name}
                title={prayer.displayName}
                text={{
                  value: `${PrayerTimeService.formatPrayerTime(prayer.time)} • ${statusText}`,
                  color: color
                }}
                icon={icon}
              />
            );
          })}

          <Detail.Metadata.Separator />

          <Detail.Metadata.Label
            title={I18nService.t('ui.locationInfo')}
            icon="📍"
          />
          <Detail.Metadata.Label
            title={I18nService.t('ui.currentLocation')}
            text={locationStr}
          />

          <Detail.Metadata.Separator />

          <Detail.Metadata.Label
            title={I18nService.t('ui.calculationSettings')}
            icon="⚙️"
          />
          <Detail.Metadata.Label
            title={I18nService.t('ui.method')}
            text={I18nService.getCalculationMethodName(prayerData.preferences.calculationMethod || 'MuslimWorldLeague')}
          />
          <Detail.Metadata.Label
            title={I18nService.t('ui.school')}
            text={I18nService.getMadhabName(prayerData.preferences.madhab || 'Shafi')}
          />

          <Detail.Metadata.Separator />

          <Detail.Metadata.Label
            title={I18nService.t('ui.lastUpdated')}
            text={prayerData.lastUpdated.toLocaleTimeString()}
            icon="🔄"
          />
        </Detail.Metadata>
      }
      actions={
        <ActionPanel>
          <Action
            title={I18nService.t('ui.refresh')}
            icon={Icon.ArrowClockwise}
            onAction={refreshPrayerTimes}
          />
          <Action.CopyToClipboard
            title={I18nService.t('ui.copyNextPrayer')}
            icon={Icon.Clipboard}
            content={`${nextPrayer.prayer.displayName} - ${timeStr}`}
          />
          <Action.CopyToClipboard
            title={I18nService.t('ui.copyAllTimes')}
            icon={Icon.CopyClipboard}
            content={allTimesContent}
          />
          <Action
            title={I18nService.t('ui.openSettings')}
            icon={Icon.Gear}
            onAction={openExtensionPreferences}
          />
        </ActionPanel>
      }
    />
  );
}
